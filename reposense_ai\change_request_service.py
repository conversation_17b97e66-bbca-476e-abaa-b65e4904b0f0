#!/usr/bin/env python3
"""
Change Request Service for RepoSense AI

This service handles integration with SQL databases to retrieve change request
information based on commit messages. It supports multiple database types and
configurable query patterns.
"""

import html
import logging
import re
from contextlib import contextmanager
from datetime import datetime
from typing import List, Optional

from models import ChangeRequestInfo, Config, SqlConfig


class ChangeRequestService:
    """Service for retrieving change request information from SQL databases"""

    def __init__(self, config: Config):
        self.config = config.sql_config
        self.logger = logging.getLogger(__name__)
        self._connection_pool = None

        # Import database drivers based on configuration
        self._db_module = None
        if self.config.enabled:
            self._initialize_database_driver()

    def _clean_html_content(self, text: str) -> str:
        """
        Clean HTML tags and entities from text content while preserving line breaks.

        Args:
            text: Text that may contain HTML tags and entities

        Returns:
            Clean text with HTML tags removed, entities decoded, and line breaks preserved
        """
        if not text or not isinstance(text, str):
            return text

        # First, convert common HTML line break tags to actual line breaks
        clean_text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)
        clean_text = re.sub(r'<p\s*/?>', '\n', clean_text, flags=re.IGNORECASE)
        clean_text = re.sub(r'</p>', '\n', clean_text, flags=re.IGNORECASE)
        clean_text = re.sub(r'<div\s*[^>]*>', '\n', clean_text, flags=re.IGNORECASE)
        clean_text = re.sub(r'</div>', '\n', clean_text, flags=re.IGNORECASE)

        # Remove all other HTML tags using regex
        # This pattern matches opening and closing tags, including self-closing tags
        clean_text = re.sub(r'<[^>]+>', '', clean_text)

        # Decode HTML entities (like &nbsp;, &lt;, etc.)
        clean_text = html.unescape(clean_text)

        # Clean up excessive whitespace while preserving line breaks
        # Replace multiple spaces/tabs with single spaces, but keep line breaks
        clean_text = re.sub(r'[ \t]+', ' ', clean_text)

        # Remove excessive consecutive line breaks (more than 2)
        clean_text = re.sub(r'\n{3,}', '\n\n', clean_text)

        # Remove leading/trailing whitespace from each line while preserving the line structure
        lines = clean_text.split('\n')
        lines = [line.strip() for line in lines]
        clean_text = '\n'.join(lines)

        # Remove leading/trailing whitespace from the entire text
        clean_text = clean_text.strip()

        return clean_text

    def _initialize_database_driver(self):
        """Initialize the appropriate database driver based on configuration"""
        try:
            if self.config.driver.lower() == "mysql":
                import pymysql

                self._db_module = pymysql
                self.logger.info(
                    "Initialized MySQL driver for change request integration"
                )

            elif self.config.driver.lower() == "postgresql":
                import psycopg2
                import psycopg2.extras

                self._db_module = psycopg2
                self.logger.info(
                    "Initialized PostgreSQL driver for change request integration"
                )

            elif self.config.driver.lower() == "sqlite":
                import sqlite3

                self._db_module = sqlite3
                self.logger.info(
                    "Initialized SQLite driver for change request integration"
                )

            elif self.config.driver.lower() == "mssql":
                import pyodbc

                self._db_module = pyodbc
                self.logger.info(
                    "Initialized SQL Server driver for change request integration"
                )

            else:
                self.logger.error(f"Unsupported database driver: {self.config.driver}")
                self.config.enabled = False

        except ImportError as e:
            self.logger.error(
                f"Failed to import database driver for {self.config.driver}: {e}"
            )
            self.logger.error("Please install the required database driver package")
            self.config.enabled = False

    def extract_change_request_numbers(self, commit_message: str) -> List[str]:
        """Extract change request numbers from commit message using configured patterns"""
        if not commit_message:
            return []

        numbers = []
        for pattern in self.config.change_request_patterns:
            try:
                matches = re.findall(pattern, commit_message, re.IGNORECASE)
                numbers.extend(matches)
            except re.error as e:
                self.logger.warning(f"Invalid regex pattern '{pattern}': {e}")

        # Remove duplicates while preserving order
        unique_numbers = []
        seen = set()
        for number in numbers:
            if number not in seen:
                unique_numbers.append(number)
                seen.add(number)

        if unique_numbers:
            self.logger.debug(
                f"Extracted change request numbers from commit message: {unique_numbers}"
            )

        return unique_numbers

    @contextmanager
    def _get_connection(self):
        """Get database connection with proper error handling"""
        if not self.config.enabled or not self._db_module:
            raise RuntimeError(
                "Change request integration is not enabled or configured"
            )

        connection = None
        try:
            if self.config.driver.lower() == "mysql":
                connection = self._db_module.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database,
                    connect_timeout=self.config.connection_timeout,
                    charset="utf8mb4",
                )

            elif self.config.driver.lower() == "postgresql":
                connection = self._db_module.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    dbname=self.config.database,
                    connect_timeout=self.config.connection_timeout,
                )

            elif self.config.driver.lower() == "sqlite":
                connection = self._db_module.connect(
                    self.config.database, timeout=self.config.connection_timeout
                )

            elif self.config.driver.lower() == "mssql":
                connection_string = (
                    f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                    f"SERVER={self.config.host},{self.config.port};"
                    f"DATABASE={self.config.database};"
                    f"UID={self.config.username};"
                    f"PWD={self.config.password};"
                    f"Encrypt=yes;"
                    f"TrustServerCertificate=no;"
                    f"Connection Timeout={self.config.connection_timeout};"
                )
                connection = self._db_module.connect(connection_string)
            else:
                raise ValueError(
                    f"Unsupported database driver: {self.config.driver}. "
                    f"Supported drivers are: mysql, postgresql, sqlite, mssql"
                )

            if connection is None:
                raise RuntimeError("Failed to establish database connection")

            yield connection

        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            raise
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    self.logger.warning(f"Error closing database connection: {e}")

    def get_change_request_info(
        self, change_request_number: str
    ) -> Optional[ChangeRequestInfo]:
        """Retrieve change request information from SQL database"""
        if not self.config.enabled:
            self.logger.debug("Change request integration is disabled")
            return None

        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # Prepare the query with parameter substitution
                query = self.config.change_request_query

                # Execute parameterized query based on database type
                if self.config.driver.lower() == "mysql":
                    # MySQL uses %s for parameters
                    query = query.replace(":change_request_number", "%s")
                    cursor.execute(query, (change_request_number,))
                elif self.config.driver.lower() == "postgresql":
                    # PostgreSQL uses %s for parameters
                    query = query.replace(":change_request_number", "%s")
                    cursor.execute(query, (change_request_number,))
                elif self.config.driver.lower() in ["sqlite", "mssql"]:
                    # Use positional parameters
                    query = query.replace(":change_request_number", "?")
                    cursor.execute(query, (change_request_number,))

                row = cursor.fetchone()
                cursor.close()

                if row:
                    return self._row_to_change_request(row)
                else:
                    self.logger.debug(
                        f"No change request found for number: {change_request_number}"
                    )
                    return None

        except Exception as e:
            self.logger.error(
                f"Error retrieving change request {change_request_number}: {e}"
            )
            return None

    def _row_to_change_request(self, row) -> ChangeRequestInfo:
        """Convert database row to ChangeRequestInfo object"""
        try:
            # Handle different row formats (tuple vs dict-like)
            if hasattr(row, "keys"):
                # Dict-like row (e.g., from psycopg2.extras.DictCursor)
                data = dict(row)
            else:
                # Tuple row - assume standard column order
                data = {
                    "id": row[0],
                    "number": row[1],
                    "title": row[2],
                    "description": row[3],
                    "priority": row[4],
                    "status": row[5],
                    "created_date": row[6],
                    "assigned_to": row[7],
                    "assigned_to_tester": row[8],
                    "category": row[9],
                    "risk_level": row[10],
                }

            # Parse created_date if it's a string
            created_date = data.get("created_date")
            if isinstance(created_date, str):
                try:
                    created_date = datetime.fromisoformat(
                        created_date.replace("Z", "+00:00")
                    )
                except ValueError:
                    created_date = None

            # Clean HTML content from text fields
            title = self._clean_html_content(str(data.get("title", "")))
            description = self._clean_html_content(str(data.get("description", "")))
            assigned_to = self._clean_html_content(str(data.get("assigned_to", ""))) if data.get("assigned_to") else None
            assigned_to_tester = self._clean_html_content(str(data.get("assigned_to_tester", ""))) if data.get("assigned_to_tester") else None
            category = self._clean_html_content(str(data.get("category", ""))) if data.get("category") else None

            return ChangeRequestInfo(
                id=str(data.get("id", "")),
                number=str(data.get("number", "")),
                title=title,
                description=description,
                priority=str(data.get("priority", "MEDIUM")),
                status=str(data.get("status", "OPEN")),
                created_date=created_date,
                assigned_to=assigned_to,
                assigned_to_tester=assigned_to_tester,
                category=category,
                risk_level=data.get("risk_level"),
            )

        except Exception as e:
            self.logger.error(
                f"Error converting database row to ChangeRequestInfo: {e}"
            )
            raise

    def get_multiple_change_requests(
        self, numbers: List[str]
    ) -> List[ChangeRequestInfo]:
        """Retrieve multiple change requests efficiently"""
        if not numbers or not self.config.enabled:
            return []

        results = []
        for number in numbers:
            cr_info = self.get_change_request_info(number)
            if cr_info:
                results.append(cr_info)

        if results:
            self.logger.info(
                f"Retrieved {len(results)} change request records from {len(numbers)} numbers"
            )
        else:
            self.logger.debug(f"No change requests found for numbers: {numbers}")

        return results

    def test_connection(self) -> bool:
        """Test database connection and configuration"""
        if not self.config.enabled:
            self.logger.info("Change request integration is disabled")
            return False

        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # Simple test query
                if self.config.driver.lower() == "mysql":
                    cursor.execute("SELECT VERSION()")
                elif self.config.driver.lower() == "postgresql":
                    cursor.execute("SELECT version()")
                elif self.config.driver.lower() == "sqlite":
                    cursor.execute("SELECT sqlite_version()")
                elif self.config.driver.lower() == "mssql":
                    cursor.execute("SELECT @@VERSION")

                result = cursor.fetchone()
                cursor.close()

                self.logger.info(
                    f"Change request database connection successful: {result[0] if result else 'Connected'}"
                )
                return True

        except Exception as e:
            self.logger.error(f"Change request database connection test failed: {e}")
            return False
